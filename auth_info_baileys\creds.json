{"noiseKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "gD+jtKBBZOzaEjZJ52lXpKcFcfFVjORCp9aySsN0DV0="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "i1T4HcnWkexwvCrdBt2xQ1dUVuLTy3gP6fY8b2jChw4="}}, "pairingEphemeralKeyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "iN4btjXB3dtU5tcaEXJVW0hM1+Axj6enWSqd+zeEMW0="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "9JkVxXiCTMlnerpt++tuPlAp6QJprO1mNIXhAq+7W3U="}}, "signedIdentityKey": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "oIMu1hMTetrySwzNS9xYYlSYC6giLU56Oqr/39L42U4="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "0pL12PJE8dyavZZGXsdFob7FbgYOwowKwV5QWbDOZEc="}}, "signedPreKey": {"keyPair": {"private": {"type": "<PERSON><PERSON><PERSON>", "data": "yJXjJgY8soSKRBVvvuGGj53mE58VI+E16EGC0rBTWU8="}, "public": {"type": "<PERSON><PERSON><PERSON>", "data": "EzqASRBuF5K2K0yrgh8iUfgoHj4jkOvr4Rp/L7iufyQ="}}, "signature": {"type": "<PERSON><PERSON><PERSON>", "data": "+9m+T1Xbw4+QMm9wa9uh6pXIlXSEflM1RPv2B95o4lPTnY1HoUrjIeHFR+oXD8K42rhPoDzsmssj2J1kMmQ4Cw=="}, "keyId": 1}, "registrationId": 121, "advSecretKey": "A55/huoivgJMK1k7pkVlKDZ1Sq3y6h6asYvl65FKAak=", "processedHistoryMessages": [{"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "83ED1A2962194A11FA104A962B546590"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "77FF101F5630BF3F3AB32649511EC44B"}, "messageTimestamp": **********}, {"key": {"remoteJid": "<EMAIL>", "fromMe": true, "id": "5038346F1F7D2292B7EBA981E50FB5A9"}, "messageTimestamp": **********}], "nextPreKeyId": 72, "firstUnuploadedPreKeyId": 72, "accountSyncCounter": 1, "accountSettings": {"unarchiveChats": false}, "registered": false, "account": {"details": "CLy9wMQGEPbU4MIGGAUgACgA", "accountSignatureKey": "INbGZlbGqDo0FchCVutm40caPTbx1kh8Rs2q8Zxjfwc=", "accountSignature": "ChAdUTCl4EPjHc4yCy2USJ8d3SJ295HwkJ/SzXci2ushoDKwQe0Ct63ww6Wy1mETmdo3ON2oG8m9pKVaaki+CA==", "deviceSignature": "bqbs84aYUwXbK1FkbJmg0R2utoapB3oQ1OuBmovdmJdQmPwXvdqf85tim+4jhvC0Miz0OsO1TIIYUKGOqKkDAQ=="}, "me": {"id": "*************:<EMAIL>", "lid": "***************:17@lid", "name": "Sihab Vau☠️"}, "signalIdentities": [{"identifier": {"name": "*************:<EMAIL>", "deviceId": 0}, "identifierKey": {"type": "<PERSON><PERSON><PERSON>", "data": "BSDWxmZWxqg6NBXIQlbrZuNHGj028dZIfEbNqvGcY38H"}}], "platform": "android", "routingInfo": {"type": "<PERSON><PERSON><PERSON>", "data": "CA0IEg=="}, "lastAccountSyncTimestamp": **********, "lastPropHash": "2V77qU", "myAppStateKeyId": "ABIAAKM3"}