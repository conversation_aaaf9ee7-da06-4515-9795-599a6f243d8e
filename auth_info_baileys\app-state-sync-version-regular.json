{"version": 3, "hash": {"type": "<PERSON><PERSON><PERSON>", "data": "6irFoSThbJAwLyaEPjLqr7gzSkzkVzduU0SfIs9trZD6oTXIzs4QtgKbyT6wsww7XcQX4CDjDIWzFMMyhXwaTCDm2nvVUahkSKXWCRpIqFG8//q9AgQuQ2jRq0c7oVgplhECPiFcFZt3VMWRWqN7ssiXy9+NUwcTZTm+DTm2pJc="}, "indexValueMap": {"FHuE25708FAawNYhQKxbTfJ9xn2uPVu4GVqEeWxHQ90=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "9lTPXtIayzMJ+9fvmtP8HgdicgAdRmAUxviIW5zc8M8="}}, "PLSjFwleJ6IC2d/eTiarH7BhrOrJUMt3t7iJfif0ZOI=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "OAPjagnswWKiqeHo2Lnx6hRhUxyzASwcYDRueFYiojk="}}, "T6Inwv7kee7tAFUvwc0kTb13xcSp+agSoqoMFcz1pTU=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "zh3covRG9IDuyLSvU+kihc/y/BkY2HkaXVO29LqeMKQ="}}, "WkWXCPNwoW9dm6nuYOTM6mabD64IBC3OYTJktoOzJkw=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "bdC6sf6zvqwEEP47A0m/pJ17YHU4fvsZU1aabw4Q2l8="}}, "wT3Lx/cZTB2FfgTWTIu3aozSFHxEhfbmqB9rMbApX5c=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "k+WDZzor8gxu8Ck2KIeC0iAflKs4xS0D/dZx3rInoow="}}, "C1OwTFdEdDvhxfJLR8ZKhpCarIuBQ6G0+Q9RnIX7zYg=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "vhUmrOrJ9bdyphIyTDOlknJtQr3serI2P+8LYrsmbnM="}}, "5w0/vcWsUDIlPCQlYVxj0yoH2E740jQ5T7jfqUShLSc=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "/7ehx9oDAdkIlEfmkzEEmefyBRSrsRkw2z+kSYSM9Xg="}}}}