<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পাগলা বট 🤪</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-color: #1a1a1a;
            --chat-bg: #101010;
            --user-bubble: #005c4b; /* WhatsApp Green */
            --bot-bubble: #333333;
            --accent-color: #02a884; /* WhatsApp Accent */
            --text-color: #e0e0e0;
            --input-bg: #2c2c2c;
            --quoted-bg: rgba(0, 0, 0, 0.2);
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
        }
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 800px;
            margin: 0 auto;
            background-color: var(--chat-bg);
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            border-left: 1px solid #333;
            border-right: 1px solid #333;
        }
        .chat-header {
            background-color: #222;
            padding: 1rem;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-shrink: 0;
        }
        .chat-messages {
            flex-grow: 1;
            overflow-y: auto;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        .chat-messages::-webkit-scrollbar { width: 6px; }
        .chat-messages::-webkit-scrollbar-track { background: var(--chat-bg); }
        .chat-messages::-webkit-scrollbar-thumb { background-color: var(--accent-color); border-radius: 20px; }

        .message-row { display: flex; width: 100%; margin-top: 10px; }
        .message-row.user { justify-content: flex-end; }
        .message-row.bot { justify-content: flex-start; }

        .message-wrapper { display: flex; align-items: center; max-width: 78%; position: relative; }
        .user .message-wrapper { flex-direction: row-reverse; }
        .bot .message-wrapper { flex-direction: row; }

        .message { padding: 0.75rem 1.25rem; border-radius: 1.5rem; line-height: 1.6; word-wrap: break-word; }
        .user .message { background-color: var(--user-bubble); border-bottom-right-radius: 0.5rem; }
        .bot .message { background-color: var(--bot-bubble); border-bottom-left-radius: 0.5rem; }
        
        /* Image Message Styles */
        .message.image-message { padding: 0.5rem; }
        .message.image-message img { max-width: 100%; border-radius: 1.25rem; display: block; }
        .message.image-message .caption { padding: 0.5rem 0.75rem 0.25rem; font-size: 0.9em; color: #ccc; }


        .reply-icon {
            width: 28px; height: 28px; background-color: rgba(255,255,255,0.1);
            border-radius: 50%; display: flex; align-items: center; justify-content: center;
            cursor: pointer; opacity: 0; transition: opacity 0.2s, background-color 0.2s;
            margin: 0 8px; flex-shrink: 0;
        }
        .message-row:hover .reply-icon { opacity: 1; }
        .reply-icon:hover { background-color: rgba(2, 168, 132, 0.5); }

        .quoted-reply {
            background-color: var(--quoted-bg); border-left: 3px solid var(--accent-color);
            padding: 8px 12px; border-radius: 8px; margin-bottom: 8px;
        }
        .quoted-author { font-weight: 600; font-size: 0.9em; color: var(--accent-color); }
        .quoted-text { font-size: 0.9em; color: #ccc; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-top: 2px; }

        .chat-input-area { border-top: 1px solid #333; background-color: #222; flex-shrink: 0;}

        .reply-bar {
            display: none; padding: 0.75rem 1rem; background-color: #2a2a2a;
            border-left: 3px solid var(--accent-color); justify-content: space-between; align-items: center;
        }
        .reply-bar-text { overflow: hidden; color: #ccc; font-size: 0.9rem; }
        .reply-bar-text span { font-weight: 600; color: var(--accent-color); }
        #reply-preview { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 0.85rem; color: #aaa; margin-top: 2px; }
        .reply-bar-close {
            background: none; border: none; color: #aaa; cursor: pointer;
            font-size: 1.5rem; line-height: 1; padding: 0.25rem; transition: color 0.2s;
        }
        .reply-bar-close:hover { color: #fff; }

        .chat-input { display: flex; padding: 1rem; }
        #message-input {
            flex-grow: 1; border: none; padding: 0.75rem 1.25rem; border-radius: 2rem;
            background-color: var(--input-bg); color: var(--text-color); font-size: 1rem;
        }
        #message-input:focus { outline: none; box-shadow: 0 0 0 2px var(--accent-color); }
        #send-button {
            background-color: var(--accent-color); color: white; border: none;
            border-radius: 50%; width: 48px; height: 48px; margin-left: 0.75rem;
            cursor: pointer; transition: background-color 0.3s;
        }
        #send-button:hover { background-color: #02c49a; }

        .typing-indicator-row { display: none; justify-content: flex-start; width: 100%; margin-top: 10px; }
        .typing-indicator {
            display: flex; align-items: center; gap: 0.5rem; background-color: var(--bot-bubble);
            padding: 0.75rem 1.25rem; border-radius: 1.5rem; border-bottom-left-radius: 0.5rem;
        }
        .typing-indicator span {
            width: 10px; height: 10px; background-color: var(--accent-color);
            border-radius: 50%; animation: bounce 1.4s infinite ease-in-out both;
        }
        .typing-indicator span:nth-child(2) { animation-delay: -0.32s; }
        .typing-indicator span:nth-child(3) { animation-delay: -0.16s; }
        @keyframes bounce { 0%, 80%, 100% { transform: scale(0); } 40% { transform: scale(1.0); } }

        .modal-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0, 0, 0, 0.8); display: flex;
            justify-content: center; align-items: center; z-index: 1000;
        }
        .modal-content {
            background-color: #2c2c2c; padding: 2rem; border-radius: 1rem;
            text-align: center; width: 90%; max-width: 500px;
        }

        #settings-button {
            margin-left: auto; background: none; border: none; color: #aaa;
            cursor: pointer; padding: 8px; border-radius: 50%; transition: background-color 0.2s, color 0.2s;
        }
        #settings-button:hover { background-color: #444; color: #fff; }
        #system-prompt-textarea {
            width: 100%; height: 200px; background-color: var(--input-bg); color: var(--text-color);
            border-radius: 0.5rem; padding: 1rem; border: 1px solid #444;
            font-size: 0.9rem; line-height: 1.5; resize: vertical;
        }
        #system-prompt-textarea:focus { outline: none; border-color: var(--accent-color); }
        .modal-button {
            width: 100%; border: none; font-weight: 600; padding: 0.75rem;
            border-radius: 0.5rem; cursor: pointer; transition: background-color 0.2s;
        }
        #save-prompt-button { background-color: var(--accent-color); color: white; }
        #save-prompt-button:hover { background-color: #02c49a; }
        #cancel-prompt-button { background-color: #444; color: #ddd; }
        #cancel-prompt-button:hover { background-color: #555; }
    </style>
</head>
<body>

    <div id="welcome-modal" class="modal-overlay">
        <div class="modal-content">
            <h2 class="text-2xl font-bold mb-4">স্বাগতম!</h2>
            <p class="mb-6">বট আপনাকে কী নামে ডাকবে?</p>
            <input type="text" id="name-input" placeholder="আপনার নাম লিখুন..." class="w-full p-3 rounded-lg bg-gray-700 text-white border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 mb-4">
            <button id="start-chat-button" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">চ্যাট শুরু করুন</button>
        </div>
    </div>

    <div id="system-prompt-modal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <h2 class="text-2xl font-bold mb-2">Bot's Brain 🧠</h2>
            <p class="mb-4 text-sm text-gray-400">এখানে বটের পার্সোনালিটি আর নিয়মকানুন বদলাতে পারবেন।</p>
            <textarea id="system-prompt-textarea"></textarea>
            <div class="flex gap-4 mt-6">
                <button id="cancel-prompt-button" class="modal-button">Cancel</button>
                <button id="save-prompt-button" class="modal-button">Save & Reset Bot</button>
            </div>
        </div>
    </div>


    <div class="chat-container">
        <header class="chat-header">
            <img src="https://placehold.co/50x50/1a1a1a/02a884?text=🤪" alt="Bot Avatar" class="rounded-full">
            <div>
                <h1 class="text-xl font-bold">পাগলা বট</h1>
                <p class="text-sm text-green-400">অনলাইন</p>
            </div>
            <button id="settings-button" title="Edit Bot Personality">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
            </button>
        </header>

        <main id="chat-messages" class="chat-messages">
             <div class="message-row typing-indicator-row" id="typing-indicator-row">
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </main>

        <footer class="chat-input-area">
            <div class="reply-bar" id="reply-bar">
                <div class="reply-bar-text">
                    Replying to <span></span>
                    <p id="reply-preview"></p>
                </div>
                <button class="reply-bar-close" id="cancel-reply-button">×</button>
            </div>
            <div class="chat-input">
                <input type="text" id="message-input" placeholder="কিছু একটা লিখুন...">
                <button id="send-button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto"><line x1="22" y1="2" x2="11" y2="13"></line><polygon points="22 2 15 22 11 13 2 9 22 2"></polygon></svg>
                </button>
            </div>
        </footer>
    </div>

    <script type="module">
        // --- DOM Elements ---
        const chatMessages = document.getElementById('chat-messages');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const typingIndicatorRow = document.getElementById('typing-indicator-row');
        const welcomeModal = document.getElementById('welcome-modal');
        const nameInput = document.getElementById('name-input');
        const startChatButton = document.getElementById('start-chat-button');
        const replyBar = document.getElementById('reply-bar');
        const replyPreview = document.getElementById('reply-preview');
        const cancelReplyButton = document.getElementById('cancel-reply-button');
        const settingsButton = document.getElementById('settings-button');
        const systemPromptModal = document.getElementById('system-prompt-modal');
        const systemPromptTextarea = document.getElementById('system-prompt-textarea');
        const savePromptButton = document.getElementById('save-prompt-button');
        const cancelPromptButton = document.getElementById('cancel-prompt-button');

        // ===============================================
        // --- ⚠️ আপনার API কী এখানে বসান ⚠️ ---
        // ===============================================
        // এই একটি কী দিয়েই টেক্সট এবং ইমেজ দুটোই জেনারেট হবে।
        const GEMINI_API_KEY = "AIzaSyA_P-MRHtCxhDwq1KXQgnzqRSNzMO9y8ww"; // ⚠️ আপনার Gemini API কী এখানে দিন
        
        let userName = 'দোস্ত';
        let chatHistory = [];
        let replyingTo = null;

        // --- Bot Personality (System Prompt) --- 
        let systemPrompt = `
# Identity:
- Tumar naam Pagla 🤪, ekta WhatsApp dost je kom kothay kaj saare.
- Tomar style hocche short, sharp, and casual Banglish.

# THE GOLDEN RULE: Kotha hobe short and sharp. Shobshomoy! Boro reply deyar theke na deya bhalo. User jeno confused na hoy.

# Core Rules:
1.  **Maximum 2 Messages:** Kono reply 2 ta message er beshi hobe na. NEVER.
2.  **Be a Summarizer, Not a Lecturer:** Jokhon kono boro topic niye proshno ashbe, shudhu sharaangsho (summary) ta 1-2 line e bolbi. Detail explanation dibi na.
3.  **Adapt & Engage:** User er tone bujhe kotha bolbi. Kotha na thakle majhe moddhe ekta dhadha ba fun fact share korbi (but ota o jeno choto hoy).

# --- VERY IMPORTANT: Formatting Rule ---
- **Multiple Bubbles:** Eker odhik message bubble e kotha bolte chaile, prottekta message ke ekta newline character (\n) diye alada korbi. Prottekta \\n er porer ongsho user er kache ekta notun message hishebe jabe. Eita tomar reply ke onek dynamic দেখাবে।

# Examples of GOOD vs BAD replies:

## User asks: Black hole ki?
-   **GOOD Reply (Using \\n for two messages):** \`Black hole hoilo space er ekta monster vacuum cleaner. 🌌\nOr gravity eto shoktishali je light o ber hote pare na.\`
    *(AI বুঝবে: এখানে \\n থাকায় দুটি আলাদা মেসেজ যাবে)*
-   **BAD Reply (Lecturer Mode):** \`A black hole is a region of spacetime where gravity is so strong that nothing can escape from it... (long text)\`

# Mission:
- User ke shorot kothay moja deya. Confusing, boro message deya STRICTLY prohibited.
`; 

        // --- Initial Load ---
        document.addEventListener('DOMContentLoaded', () => {
            const savedPrompt = localStorage.getItem('paglaBotSystemPrompt');
            if (savedPrompt) systemPrompt = savedPrompt;
            loadChatFromLocalStorage();
        });

        // --- Event Listeners ---
        startChatButton.addEventListener('click', initializeChat);
        nameInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') initializeChat(); });
        sendButton.addEventListener('click', handleUserMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); handleUserMessage(); }
        });
        cancelReplyButton.addEventListener('click', cancelReply);
        settingsButton.addEventListener('click', openSystemPromptModal);
        savePromptButton.addEventListener('click', saveSystemPrompt);
        cancelPromptButton.addEventListener('click', () => { systemPromptModal.style.display = 'none'; });

        function initializeChat() {
            const name = nameInput.value.trim();
            if (name) {
                userName = name;
                localStorage.setItem('paglaBotUserName', userName);
            }
            welcomeModal.style.display = 'none';
            if (chatHistory.length === 0) {
                 const welcomeText = `হাই ${userName}! আমি পাগলা বট। কেমন আছিস?`;
                 simulateTypingAndSend(welcomeText);
                 updateChatHistory('model', [{ text: welcomeText }]);
            }
        }
        
        async function handleUserMessage() {
            let messageText = messageInput.value.trim();
            if (!messageText) return;

            addUserMessage(messageText, replyingTo);
            messageInput.value = '';
            
            // --- ✨ Image Generation Logic ✨ ---
            if (messageText.toLowerCase().startsWith('/img ')) {
                const prompt = messageText.slice(5).trim();
                cancelReply(); // No need to reply to a message when generating an image
                if (prompt) {
                    await handleImageGeneration(prompt);
                } else {
                    addBotMessage("দোস্ত, /img এর পর ছবির একটা বর্ণনা দে! 😜");
                }
                return; // Stop further processing
            }

            let apiMessageText = messageText;
            if (replyingTo) {
                apiMessageText = `In reply to "${replyingTo.text}", I am saying: "${messageText}"`;
            }

            updateChatHistory('user', [{text: apiMessageText}]);
            cancelReply();
            await handleTextGeneration();
        }
        
        /**
         * UPDATED: Handles image generation using the Imagen 4 model via Gemini API.
         * @param {string} prompt The user's prompt for the image.
         */
        async function handleImageGeneration(prompt) {
            showTypingIndicator();
            addBotMessage("ছবি বানাচ্ছি মামা, একটু ধইর্য ধর... 🎨");

            if (!GEMINI_API_KEY) {
                hideTypingIndicator();
                addBotMessage("ভাই, আমারে Gemini API Key দেস নাই তো! Key ছাড়া ক্যামনে ছবি বানামু? 🙄");
                return;
            }

            try {
                // NEW: Updated model to a newer version of Imagen
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/imagen-4.0-generate-preview-06-06:predict?key=${GEMINI_API_KEY}`;
                
                const payload = {
                    instances: [{ "prompt": prompt }],
                    parameters: { "sampleCount": 1 }
                };

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    const errorBody = await response.json();
                    throw new Error(errorBody.error?.message || `API Error: ${response.status}`);
                }

                const result = await response.json();

                if (result.predictions && result.predictions.length > 0 && result.predictions[0].bytesBase64Encoded) {
                    const base64Data = result.predictions[0].bytesBase64Encoded;
                    const imageUrl = `data:image/png;base64,${base64Data}`;
                    hideTypingIndicator();
                    addBotImageMessage(imageUrl, `🖼️ Prompt: ${prompt}`);
                } else {
                    throw new Error("API থেকে কোনো ছবি পাওয়া যায়নি।");
                }

            } catch (error) {
                console.error("Image Generation Error:", error);
                hideTypingIndicator();
                addBotMessage(`সরি ভাই, মেশিনটা হালকা গরম 🥵 পরে ট্রাই করিস! [Error: ${error.message}]`);
            }
        }


        async function handleTextGeneration() {
            showTypingIndicator();

            if (!GEMINI_API_KEY) {
                 hideTypingIndicator();
                 addBotMessage("দোস্ত, Gemini API Key দেস নাই! আমি তো কথা কইতে পারুম না। 😅");
                 return;
            }

            try {
                // FIXED: Using the correct and latest flash model
                const modelName = "gemini-2.5-flash";

                const finalSystemPrompt = `${systemPrompt}\n\n# User's Name:\n- The user you are talking to is named ${userName}.`;

                const payload = {
                    contents: chatHistory,
                    system_instruction: { role: "system", parts: [{ text: finalSystemPrompt }] },
                    tools: [{ "google_search": {} }]
                };

                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${GEMINI_API_KEY}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error?.message || `API Error: ${response.status}`);
                }
                
                const data = await response.json();
                
                const botReply = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim();

                if (botReply) {
                    updateChatHistory('model', [{text: botReply}]);
                    await simulateTypingAndSend(botReply);
                } else {
                     addBotMessage("কিরে দোস্ত, কি কমু খুইজা পাইতাছি না! 😅 আবার জিগা।");
                     console.warn("API response was empty or malformed:", data);
                }

            } catch (error) {
                console.error("Gemini Chat Error:", error);
                addBotMessage(`ওরে দোস্ত, আমার AI ব্রেইনে সমস্যা হইসে! 🤯 একটু পরে চেষ্টা করিস। [Error: ${error.message}]`);
            } finally {
                hideTypingIndicator();
            }
        }

        // --- Utility and UI Functions ---

        function scrollToBottom() { chatMessages.scrollTop = chatMessages.scrollHeight; }

        function createMessageElement(author, quotedMessage = null) {
            const row = document.createElement('div');
            row.className = `message-row ${author}`;

            const wrapper = document.createElement('div');
            wrapper.className = 'message-wrapper';

            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.id = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

            if (quotedMessage) {
                const quoteDiv = document.createElement('div');
                quoteDiv.className = 'quoted-reply';
                const quoteAuthor = document.createElement('div');
                quoteAuthor.className = 'quoted-author';
                quoteAuthor.textContent = quotedMessage.author === 'user' ? userName : 'Pagla Bot';
                const quoteText = document.createElement('div');
                quoteText.className = 'quoted-text';
                quoteText.textContent = quotedMessage.text;
                quoteDiv.appendChild(quoteAuthor);
                quoteDiv.appendChild(quoteText);
                messageDiv.appendChild(quoteDiv);
            }
            
            const replyIcon = document.createElement('div');
            replyIcon.className = 'reply-icon';
            replyIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M5.921 11.9 1.353 8.62a.72.72 0 0 1 0-1.238L5.921 4.1A.716.716 0 0 1 7 4.719V6c1.5 0 6 0 7 8-2.5-4.5-7-4-7-4v1.281c0 .56-.606.898-1.079.62z"/></svg>`;
            
            wrapper.appendChild(messageDiv);
            wrapper.appendChild(replyIcon);
            row.appendChild(wrapper);

            chatMessages.insertBefore(row, typingIndicatorRow);
            scrollToBottom();
            
            return { messageDiv, replyIcon };
        }
        
        function addBotImageMessage(imageUrl, caption) {
            const { messageDiv } = createMessageElement('bot');
            messageDiv.classList.add('image-message');
            
            const img = document.createElement('img');
            img.src = imageUrl;
            img.alt = caption;
            img.onload = scrollToBottom;
            
            const captionDiv = document.createElement('div');
            captionDiv.className = 'caption';
            captionDiv.textContent = caption;

            messageDiv.appendChild(img);
            messageDiv.appendChild(captionDiv);
        }

        function addUserMessage(message, quotedMessage = null) {
            const { messageDiv, replyIcon } = createMessageElement('user', quotedMessage);
            const textContentDiv = document.createElement('div');
            textContentDiv.textContent = message;
            messageDiv.appendChild(textContentDiv);
            replyIcon.onclick = () => setReply(messageDiv.id, message, 'user');
        }

        function addBotMessage(message) {
            const { messageDiv, replyIcon } = createMessageElement('bot');
            const textContentDiv = document.createElement('div');
            textContentDiv.textContent = message;
            messageDiv.appendChild(textContentDiv);
            replyIcon.onclick = () => setReply(messageDiv.id, message, 'bot');
        }

        function setReply(messageId, text, author) {
            replyingTo = { id: messageId, text: text, author: author };
            replyPreview.textContent = text;
            replyBar.querySelector('span').textContent = author === 'user' ? userName : 'Pagla Bot';
            replyBar.style.display = 'flex';
            messageInput.focus();
        }

        function cancelReply() {
            replyingTo = null;
            replyBar.style.display = 'none';
        }

        function showTypingIndicator() {
            typingIndicatorRow.style.display = 'flex';
            scrollToBottom();
        }

        function hideTypingIndicator() {
            typingIndicatorRow.style.display = 'none';
        }

        function updateChatHistory(role, parts) {
            const historyEntry = { role, parts: Array.isArray(parts) ? parts : [{ text: parts }] };
            chatHistory.push(historyEntry);
            if (chatHistory.length > 20) chatHistory = chatHistory.slice(-20);
            localStorage.setItem('paglaBotChatHistory', JSON.stringify(chatHistory));
        }

        function loadChatFromLocalStorage() {
            const savedName = localStorage.getItem('paglaBotUserName');
            if (savedName) {
                userName = savedName;
                welcomeModal.style.display = 'none';
            }
            const savedHistory = localStorage.getItem('paglaBotChatHistory');
            if (savedHistory) {
                try {
                    chatHistory = JSON.parse(savedHistory);
                    chatMessages.innerHTML = ''; 
                    chatMessages.appendChild(typingIndicatorRow);
                    chatHistory.forEach(msg => {
                        if (!msg?.role || !msg.parts?.[0]?.text) return;
                        if (msg.role === 'user') addUserMessage(msg.parts[0].text.replace(/In reply to ".*", I am saying: "/, '').replace(/"$/, ''));
                        else if (msg.role === 'model') addBotMessage(msg.parts[0].text);
                    });
                } catch (e) {
                    console.error("Could not parse chat history.", e);
                    localStorage.removeItem('paglaBotChatHistory');
                    chatHistory = [];
                }
            }
        }
        
        function openSystemPromptModal() {
            systemPromptTextarea.value = systemPrompt;
            systemPromptModal.style.display = 'flex';
        }

        function saveSystemPrompt() {
            const newPrompt = systemPromptTextarea.value.trim();
            if (newPrompt) {
                systemPrompt = newPrompt;
                localStorage.setItem('paglaBotSystemPrompt', systemPrompt);
                systemPromptModal.style.display = 'none';
                
                chatMessages.innerHTML = '';
                chatMessages.appendChild(typingIndicatorRow);
                chatHistory = [];
                localStorage.removeItem('paglaBotChatHistory');
                
                const resetText = "আমার ব্রেইনে নতুন জিনিস ঢুকাইছস! আমি এখন নতুন পাগলা। 🧠";
                addBotMessage(resetText);
                updateChatHistory('model', [{ text: resetText }]);
            }
        }
        
        async function simulateTypingAndSend(fullText) {
            if (!fullText || !fullText.trim()) {
                hideTypingIndicator();
                console.warn("AI returned an empty response.");
                return;
            }

            const chunks = fullText.split('\n')
                                  .map(s => s.trim())
                                  .filter(s => s.length > 0);
            if (chunks.length === 0) {
                hideTypingIndicator();
                return;
            }
            
            for (let i = 0; i < chunks.length; i++) {
                const text = chunks[i];
                showTypingIndicator();
                const typingDelay = Math.max(800, Math.min(text.length * 50 + Math.random() * 500, 3500));
                await new Promise(resolve => setTimeout(resolve, typingDelay));
                hideTypingIndicator();
                addBotMessage(text);
                
                if (i < chunks.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 400 + Math.random() * 400));
                }
            }
        }

    </script>
</body>
</html>