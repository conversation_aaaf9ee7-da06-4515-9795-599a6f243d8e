{"version": 8, "hash": {"type": "<PERSON><PERSON><PERSON>", "data": "CZoyo5mvJNpPJSznpejpESsJKc2eSVkdoFB1DcSRnVzTe1MsJ5gwQW8n69uE1DFBLE1i/lDOOGJK1EODZbiGQIzQV/ARkrv+btE7yI0RbAIn4fP4qnVjYbpUepecQa9slj7I3qwfMm3ydKnPgweOMJP8zCBzm2PyFmKuYDsQR2c="}, "indexValueMap": {"BdFblXq54kwjxF0kN3HCF+KXCWlia2LlQ/yEZUQ/AeQ=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "kgd4e4R3LenTqsdJtfGCAfZY6YjDrULSgblzwAb0iHU="}}, "FuANbusyQp/nde/rUcGK45YZbpO1toRLsje/Po+Kr/Q=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "Ux91YKtrDfA+4uOTDN/1twGFTsqFhkRhiUJELx/h/Y0="}}, "Po1LaIyYvfn5LqIX/8cAD7m4S01YVv7/6bQb75TOrik=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "UOCtzLWt7AkveJT0PzIzfwnWRvczA/IyVJ67enVZvyQ="}}, "Ry/ZwKewinR55Wci3PF2vpHfuPpe167TK+gZziIPKao=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "rb4SEKp3vkzQYoclTVGjhwu1va8+hQ8crtrO5U9UBag="}}, "Sx6aNLefA5GbQPt+2fpRqzDnO9j8MIGh8goCIjMrDv4=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "l2k9+VPdLumkHIkrxXTXZ/gSyt1Og5Bv8dmAcMj2ddU="}}, "cnob8cPo2NdVaxN9ScmQSS4LFKDddkJLQa/F2BbkesA=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "dwKGIk2P5VP5CsqHmXXcLKZKkEdO38J2K9ptXctUIj4="}}, "dG13EZGddOOfKTAL/crbdi9CPQNjXSPThlLqfZ/eicI=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "xjZAfUUMRTNG4EzYEcygp96XttBtEh97bnnwHbFq7Jk="}}, "eD0JJPCMaNQxHfu7dG8EhGrnUpTOvaGZC2MJSKTKS5k=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "Nf/sMJHROWSWqJsp9eyEwoaoMKFvrKC8d2p5YyTqFLA="}}, "saJBypCeY+sRn/BiOHXDGJUUfBqu/g+xrbypIXu0iEQ=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "BkdwdztqbJNq4lfFZGHM7R+QhgbCNEiG/7RD7NUPO9M="}}, "se6eYWZ7+BzTrFcFMYoov+OfBaOjfc/wBi/7VvRs/yI=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "aplcRVe/7NVICRN365ybdWPWcTsQ99eW4Xo+ScKwNh0="}}, "/ajgUpMga8d/wBrgj5wi+ILV12eV1C2MCrhXpb33YZE=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "wSNu722w5ZFScxk42+62pJvzgyAcOxsI7wM0FWz8Oas="}}}}